@file:Suppress("<PERSON><PERSON><PERSON><PERSON>eng<PERSON>", "TooGenericExceptionCaught")

package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.push
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.CameraJointDetectionManager
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.compont.DeleteDialog
import com.superhexa.supervision.feature.miwear.speechhub.compont.EndRecording
import com.superhexa.supervision.feature.miwear.speechhub.compont.LoadingDialog
import com.superhexa.supervision.feature.miwear.speechhub.compont.RecordItem
import com.superhexa.supervision.feature.miwear.speechhub.compont.RenameDialog
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.RecordShare
import com.superhexa.supervision.filetrans.component.MediaSpaceManager
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.MIWEAR_RECORD_FRAGMENT
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.miwearglasses_TRANSCRIPTION_FRAGMENT
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.commonbean.BinderWrapperBean
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.RECORD_EXP_PAGE_FROM_HISTORY
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.safeActivity
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF
import com.superhexa.supervision.library.base.basecommon.theme.Color17CBFF_30
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9
import com.superhexa.supervision.library.base.basecommon.theme.Color26EAD9_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_100
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.tools.ImmersiveManager
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil.setTransparent
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.base.superhexainterfaces.miwear.IMiWearModuleApi
import com.superhexa.supervision.library.db.AudioTranscriptionDbHelper
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.db.bean.SummaryStatus
import com.superhexa.supervision.library.db.bean.TranscriptionStatus
import com.superhexa.supervision.library.statistic.O95Statistic
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

/**
 * 录音处理状态枚举
 */
enum class RecordingProcessStatus {
    NOT_STARTED, // 未开始处理
    UPLOADING, // 上传中
    TRANSCRIBING, // 转写中
    TRANSCRIPTION_COMPLETED, // 转写完成
    TRANSCRIPTION_FAILED, // 转写失败
    SUMMARIZING, // 总结中
    SUMMARY_COMPLETED, // 总结完成
    SUMMARY_FAILED // 总结失败
}

/**
 * 录音处理状态工具类
 */
object RecordingProcessStatusHelper {
    /**
     * 获取录音的处理状态
     * 直接使用数据库中的状态字段
     * 优先级：转写状态 > 总结状态
     * 特殊处理：当转写状态为NONE但总结状态不为NONE时，说明是重新总结场景，应根据总结状态显示
     */
    fun getProcessStatus(audioBean: AudioTranscriptionBean): RecordingProcessStatus {
        // 使用数据库中的转写状态
        val transcriptionStatus = audioBean.getTranscriptionStatusEnum()
        val summaryStatus = audioBean.getSummaryStatusEnum()

        // 根据数据库中的转写状态判断
        return when (transcriptionStatus) {
            TranscriptionStatus.NONE -> {
                // 特殊处理：如果转写状态是NONE但总结状态不是NONE，说明是重新总结场景
                if (summaryStatus != SummaryStatus.NONE) {
                    getProcessStatusFromSummary(summaryStatus)
                } else {
                    RecordingProcessStatus.NOT_STARTED
                }
            }
            TranscriptionStatus.UPLOADING -> RecordingProcessStatus.UPLOADING
            TranscriptionStatus.TRANSCRIBING -> RecordingProcessStatus.TRANSCRIBING
            TranscriptionStatus.SUCCESS -> {
                // 转写成功，检查总结状态
                getProcessStatusFromSummary(summaryStatus)
            }
            TranscriptionStatus.FAILED -> RecordingProcessStatus.TRANSCRIPTION_FAILED
        }
    }

    /**
     * 根据总结状态获取处理状态
     */
    private fun getProcessStatusFromSummary(summaryStatus: SummaryStatus): RecordingProcessStatus {
        return when (summaryStatus) {
            SummaryStatus.NONE -> RecordingProcessStatus.TRANSCRIPTION_COMPLETED
            SummaryStatus.SUMMARIZING -> RecordingProcessStatus.SUMMARIZING
            SummaryStatus.SUCCESS -> RecordingProcessStatus.SUMMARY_COMPLETED
            SummaryStatus.FAILED -> RecordingProcessStatus.SUMMARY_FAILED
        }
    }
}

/**
 * 类描述:
 * 创建日期: 2025/3/8 on 10:43
 * 作者: qintaiyuan
 */
@Route(path = MIWEAR_RECORD_FRAGMENT)
class NormalRecordingListFragment : BaseComposeFragment() {

    companion object {
        private const val RENAME_MAX_LENGTH = 30
    }

    private val isShowDelete = mutableStateOf(false)
    private val isShowEdit = mutableStateOf(false)
    private val isShowLoading = mutableStateOf(false)
    private val viewModel by instance<RecordingListViewModel>()
    private var changeFileName = ""
    private var pageFrom: String = ""

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.sendEvent(RecordListEvent.Init(lifecycleOwner = viewLifecycleOwner))
        requireActivity().window.setSoftInputMode(
            WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        )
        pageFrom = arguments?.getString(BundleKey.RECORD_EXP_PAGE_FROM) ?: ""
        viewModel.deviceStateLiveData?.runCatching {
            observeState(viewLifecycleOwner, O95State::deviceState) {
                launch { viewModel.deviceConnected() }
            }
        }
        O95Statistic.exposeRecord()

        // 初始化转写状态
        initTranscriptionStatus()
    }

    override fun onResume() {
        super.onResume()
        Timber.i("NormalRecordingListFragment onResume called")
        viewModel.getRecordState()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.stopPlay()
    }

    override val contentView: @Composable () -> Unit = {
        val itemList by viewModel.mutableList.observeAsState(emptyList())
        val audioList by viewModel.audioMutableList.collectAsState(emptyList())
        val effect by viewModel.effect.collectAsState(initial = null)
        val observeAsState = viewModel.deviceStateLiveData?.observeAsState()

        LaunchedEffect(effect) {
            handleEffect(effect)
        }

        ConstraintLayout(
            modifier = Modifier
                .then(if (ImmersiveManager.isImmersive.value) Modifier.statusBarsPadding() else Modifier)
                .fillMaxSize()
                .background(color = ColorBlack)
        ) {
            val (title, space, emptyView, list, bottomBtn) = createRefs()
            CommonTitleBar(
                stringResource(R.string.title_record),
                modifier = Modifier
                    .constrainAs(title) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                true,
                rightIcRes = R.drawable.right_enter_fragment_icon,
                rightIcClick = {
                    // 跳转新手引导页是沉浸式UI, 如果不设置沉浸式UI, 动画过程中页面UI会上跳
                    // 不考虑返回以后的场景, 新手引导页销毁的时候会恢复
                    immersiveStatusBar()
                    navigator.push(
                        ARouterTools.navigateToFragment(miwearglasses_TRANSCRIPTION_FRAGMENT)::class
                    ) {
                        applySlideInOut()
                    }
                }
            ) { navigator.pop() }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(space) {
                        top.linkTo(title.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        height = Dimension.wrapContent
                    }
                    .animateContentSize()
            ) {
                MediaSpaceManager(
                    bottomSpace = Dp_0,
                    isChannelSuccess = observeAsState?.value?.deviceState?.isChannelSuccess() ?: false
                ) {
                    CameraJointDetectionManager.checkIsChannelSuccess {
                        CameraJointDetectionManager.checkIsJointState {
                            IMiWearModuleApi::class.java.impl.startTrans(this@NormalRecordingListFragment)
                        }
                    }
                }
            }
            if (itemList.isNullOrEmpty()) {
                EmptyRecordingView(emptyView, bottomBtn)
            } else {
                CreateList(list, space, bottomBtn, itemList, audioList)
            }
//            if (BuildConfig.DEBUG) {
//                Text(
//                    text = "录音接口调试",
//                    style = TextStyle(
//                        color = ColorWhite,
//                        fontSize = Sp_16
//                    ),
//                    modifier = Modifier
//                        .constrainAs(test) {
//                            bottom.linkTo(title.bottom)
//                            end.linkTo(parent.end, margin = Dp_20)
//                        }
//                        .clickDebounce {
//                            navigator.push(
//                                RecordTestFragment::class
//                            ) {
//                                applySlideInOut()
//                            }
//                        }
//                )
//            }
            BottomButton(bottomBtn, viewModel)
            InitDialog()
        }
    }

    @Composable
    private fun InitDialog() {
        DeleteDialog(
            isShowDelete,
            {
                isShowDelete.value = false
                if (viewModel.curMediaBean.value?.path == viewModel.mState.value.currentPlayingPath) {
                    viewModel.stopPlay()
                }
                viewModel.toDeleteMediaFile(true)
            },
            { isShowDelete.value = false }
        )
        val reNameTitleButton = ButtonParams(text = stringResource(R.string.action_confirm))
        RenameDialog(
            titleId = R.string.title_edit_file_name,
            inputText = changeFileName.ifEmpty { getString(R.string.text_call_record) },
            placeHolder = changeFileName.ifEmpty { getString(R.string.text_call_record) },
            visible = isShowEdit.value,
            maxLength = RENAME_MAX_LENGTH,
            confirmButton = reNameTitleButton,
            onDismiss = {
                changeFileName = ""
                isShowEdit.value = false
            },
            onConfirm = { fileName: String, _ ->
                changeFileName = ""
                isShowEdit.value = false
                viewModel.toRenameMediaFile(fileName)
            }
        )
        LoadingDialog(
            tip = stringResource(R.string.text_share_convert_mp3_tip),
            visible = isShowLoading.value
        ) {
            isShowLoading.value = false
            OggDecodeHelper.oggConvertCancel()
        }
    }

    @Composable
    private fun ConstraintLayoutScope.CreateList(
        list: ConstrainedLayoutReference,
        title: ConstrainedLayoutReference,
        bottomBtn: ConstrainedLayoutReference,
        itemList: List<MediaBean>,
        audioList: List<AudioTranscriptionBean>
    ) {
        val listState = rememberLazyListState()
        LaunchedEffect(audioList) {
            listState.scrollToItem(0)
        }
        LazyColumn(
            state = listState,
            modifier = Modifier
                .padding(start = Dp_12, end = Dp_12)
                .constrainAs(list) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(title.bottom)
                    bottom.linkTo(bottomBtn.top)
                    height = Dimension.fillToConstraints
                },
            verticalArrangement = Arrangement.spacedBy(Dp_12),
            contentPadding = PaddingValues(top = Dp_12)
        ) {
            items(audioList, key = { item -> item.path }) { item ->
                Spacer(
                    modifier = Modifier
                        .width(Dp_12)
                        .background(color = Color.White)
                )
                val mediaItem = itemList.find { it.path == item.path }
//                Timber.d("createList called isFirstShow:${item.isFirstShow}")
                if (mediaItem != null) {
                    RecordItem(
                        mediaItem,
                        item,
                        viewModel = viewModel,
                        editAction = {
                            viewModel.sendEvent(RecordListEvent.ShowRenameDialog(it))
                            O95Statistic.clickRecordOption("edit_button")
                        },
                        deleteAction = {
                            viewModel.sendEvent(RecordListEvent.ShowDeleteDialog)
                            O95Statistic.clickRecordOption("delete_button")
                        },
                        onValueChange = {
                            viewModel.sendEvent(RecordListEvent.SeekTo(it))
                        },
                        onClick = {
                            viewModel.stopPlay()

                            // 重置录音的转写及总结状态
                            resetRecordingStatus(mediaItem)

                            val bundle = Bundle()
                            bundle.putBinder(BundleKey.Record, BinderWrapperBean(mediaItem))
                            <EMAIL>(
                                ARouterTools.navigateToFragment(RouterKey.MIWEAR_RECORD_TRANSCRIPTION_FRAGMENT)::class
                            ) {
                                arguments = bundle
                                applySlideInOut()
                            }
                            viewModel.toUpdateIsFirstShow(item)
                            O95Statistic.clickRecordOption("click_list")
                        }
                    )
                } else {
                    Timber.d("未找到匹配的 MediaBean: ${item.path}")
                }
            }
        }
    }

    private fun immersiveStatusBar() {
        safeActivity()?.apply {
            ImmersiveManager.setImmersive(true)
            setTransparent(this)
        }
    }

    private fun handleEffect(effect: RecordListEffect?) {
        Timber.i("handleEffect $effect")
        when (effect) {
            is RecordListEffect.ShowDeleteDialog -> {
                isShowDelete.value = true
            }

            is RecordListEffect.ShowRenameDialog -> {
                val fileName = effect.bean.fileName
                changeFileName = if (!TextUtils.isEmpty(fileName)) {
                    fileName
                } else {
                    DateTimeUtils.convertTimeStampToString(effect.bean.fileAdded)
                }
                isShowEdit.value = true
                Timber.d("ShowRenameDialog rename: ${effect.bean.fileName}, $changeFileName")
            }

            is RecordListEffect.ShareItem -> {
                Timber.d("share path: ${effect.bean.path}")
                effect.bean.let {
                    val filePath = it.path
                    filePath?.apply {
                        OggDecodeHelper.oggDecode(requireActivity(), this) { showLoading, convertStatus ->
                            if (showLoading) {
                                isShowLoading.value = true
                            } else {
                                isShowLoading.value = false
                                if (convertStatus) {
                                    RecordShare.share(requireActivity(), it, this)
                                } else {
                                    toast(R.string.record_share_convert_tip)
                                }
                            }
                        }
                    }
                }
            }

            else -> {}
        }
    }

    @Composable
    private fun ConstraintLayoutScope.BottomButton(
        bottomBtn: ConstrainedLayoutReference,
        viewModel: RecordingListViewModel
    ) {
        val isRecording by viewModel.isRecording.collectAsState(false)
        Row(
            modifier = Modifier.Companion
                .constrainAs(bottomBtn) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                }
                .padding(
                    start = if (isRecording) Dp_20 else Dp_28,
                    top = Dp_20,
                    end = if (isRecording) Dp_20 else Dp_28,
                    bottom = if (isRecording) Dp_20 else Dp_28
                )
        ) {
            val deviceConnected by viewModel.deviceConnectState.collectAsState(true)
            val canRecord = remember { mutableStateOf(false) }

            Timber.d("deviceConnected : $deviceConnected")
            if (isRecording) {
                canRecord.value = false
                EndRecording(true, viewModel) {
                    viewModel.stopNormalRecord()
                }
            } else if (pageFrom != RECORD_EXP_PAGE_FROM_HISTORY) {
                SubmitButton(
                    textColor = ColorBlack,
                    subTitle = stringResource(R.string.text_start_record),
                    enable = true,
                    enableColors = listOf(Color26EAD9, Color17CBFF),
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
                    modifier = Modifier
                        .weight(1f)
                        .height(Dp_50)
                ) {
                    if (!canRecord.value) {
                        canRecord.value = true
                        val isWeared = viewModel.deviceStateLiveData?.value?.isWeared
                        Timber.d("startRecord isWeared:$isWeared")
                        if (deviceConnected && isWeared == true) {
                            viewModel.startNormal(canRecord)
                            O95Statistic.clickRecordOption("record_begin_button")
                        } else {
                            canRecord.value = false
                            toast(R.string.record_bt_disconnect_tip)
                        }
                    }
                }
            }
        }
    }

    @Composable
    private fun ConstraintLayoutScope.EmptyRecordingView(
        emptyView: ConstrainedLayoutReference,
        bottomBtn: ConstrainedLayoutReference
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(emptyView) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(bottomBtn.top)
                }
        ) {
            Image(
                painter = painterResource(id = R.drawable.empty_record_icon),
                contentDescription = "empty_tips",
                contentScale = ContentScale.Inside,
                modifier = Modifier
                    .size(Dp_100)
            )
            Spacer(modifier = Modifier.height(Dp_12))
            Text(
                text = stringResource(R.string.text_no_recording_files),
                fontSize = Sp_16,
                fontWeight = FontWeight.W400,
                color = ColorWhite
            )
            Spacer(modifier = Modifier.height(Dp_12))
            Text(
                text = stringResource(R.string.text_no_recording_tip),
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                color = ColorWhite.copy(alpha = 0.5f),
                textAlign = TextAlign.Center
            )
        }
    }

    /**
     * 初始化录音处理状态
     * 监听数据库中音频列表的变化，当数据库状态发生变化时同步更新UI状态
     */
    private fun initTranscriptionStatus() {
        lifecycleScope.launch {
            // 观察音频列表变化，当数据库状态发生变化时更新UI处理状态
            viewModel.audioMutableList.collect { audioList ->
                Timber.d("录音列表数据库状态变化，开始同步UI状态，音频数量: ${audioList.size}")

                audioList.forEach { audioBean ->
                    // 获取当前UI中显示的状态
                    val currentStatus = viewModel.getProcessStatus(audioBean.path)

                    // 根据数据库状态计算新的处理状态
                    val newProcessStatus = RecordingProcessStatusHelper.getProcessStatus(audioBean)

                    // 记录状态变化信息
                    Timber.d(
                        "录音状态检查: filePath=${audioBean.path}, " +
                            "数据库转写状态=${audioBean.transcriptionStatus}, " +
                            "数据库总结状态=${audioBean.summaryStatus}, " +
                            "当前UI状态=$currentStatus, " +
                            "计算出的新状态=${newProcessStatus.name}"
                    )

                    // 检查是否应该更新状态
                    val shouldUpdate = shouldUpdateProcessStatus(currentStatus, newProcessStatus.name)

                    if (shouldUpdate) {
                        Timber.i("更新录音UI状态: filePath=${audioBean.path}, $currentStatus -> ${newProcessStatus.name}")
                        viewModel.updateProcessStatus(audioBean.path, newProcessStatus.name)
                    } else {
                        Timber.d("跳过录音状态更新: filePath=${audioBean.path}, 状态无需变化")
                    }
                }
            }
        }
    }

    /**
     * 判断是否应该更新处理状态
     * 基于数据库状态的更新逻辑，主要检查状态是否真的发生了变化
     */
    private fun shouldUpdateProcessStatus(
        currentStatus: String?,
        newStatus: String
    ): Boolean {
        // 如果状态没有变化，不需要更新
        return currentStatus != newStatus
    }

    /**
     * 重置录音的转写及总结状态
     * 当用户点击进入录音详情时调用，清除之前的状态指示器
     */
    private fun resetRecordingStatus(mediaBean: MediaBean) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                Timber.i("重置录音状态: ${mediaBean.fileName}")

                // 重置转写状态为NONE
                AudioTranscriptionDbHelper.updateTranscriptionStatus(mediaBean, TranscriptionStatus.NONE)

                // 重置总结状态为NONE
                AudioTranscriptionDbHelper.updateSummaryStatus(mediaBean, SummaryStatus.NONE)

                Timber.i("成功重置录音状态: ${mediaBean.fileName}")
            } catch (e: Exception) {
                Timber.e(e, "重置录音状态失败: ${mediaBean.fileName}")
            }
        }
    }
}
